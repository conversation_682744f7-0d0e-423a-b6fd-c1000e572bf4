apiVersion: v1
kind: Service
metadata:
  name: mongodb-shard2
spec:
  selector:
    app: mongodb-shard2
  clusterIP: None
  ports:
  - port: 27018
    targetPort: 27018
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mongodb-shard2
spec:
  serviceName: mongodb-shard2
  replicas: 1
  selector:
    matchLabels:
      app: mongodb-shard2
  template:
    metadata:
      labels:
        app: mongodb-shard2
    spec:
      containers:
      - name: mongodb-shard2
        image: mongo:8.0
        command:
          - mongod
          - "--shardsvr"
          - "--replSet"
          - shard2
          - "--dbpath"
          - /data/db
          - "--bind_ip"
          - 0.0.0.0
          - "--port"
          - "27018"
        ports:
        - containerPort: 27018
        volumeMounts:
        - name: mongodb-shard2-data
          mountPath: /data/db
  volumeClaimTemplates:
  - metadata:
      name: mongodb-shard2-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: mongodb-storage
      resources:
        requests:
          storage: 1Gi