apiVersion: v1
kind: Service
metadata:
  name: mongodb-shard5
spec:
  selector:
    app: mongodb-shard5
  clusterIP: None
  ports:
  - port: 27018
    targetPort: 27018
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mongodb-shard5
spec:
  serviceName: mongodb-shard5
  replicas: 0
  selector:
    matchLabels:
      app: mongodb-shard5
  template:
    metadata:
      labels:
        app: mongodb-shard5
    spec:
      containers:
      - name: mongodb-shard5
        image: mongo:8.0
        command:
          - mongod
          - "--shardsvr"
          - "--replSet"
          - shard5
          - "--dbpath"
          - /data/db
          - "--bind_ip"
          - 0.0.0.0
          - "--port"
          - "27018"
        ports:
        - containerPort: 27018
        volumeMounts:
        - name: mongodb-shard5-data
          mountPath: /data/db
  volumeClaimTemplates:
  - metadata:
      name: mongodb-shard5-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: mongodb-storage
      resources:
        requests:
          storage: 1Gi