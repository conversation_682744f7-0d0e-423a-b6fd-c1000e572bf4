apiVersion: v1
kind: Service
metadata:
  name: mongodb-shard1
spec:
  selector:
    app: mongodb-shard1
  clusterIP: None
  ports:
  - port: 27018
    targetPort: 27018
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mongodb-shard1
spec:
  serviceName: mongodb-shard1
  replicas: 1
  selector:
    matchLabels:
      app: mongodb-shard1
  template:
    metadata:
      labels:
        app: mongodb-shard1
    spec:
      containers:
      - name: mongodb-shard1
        image: mongo:8.0
        command:
          - mongod
          - "--shardsvr"
          - "--replSet"
          - shard1
          - "--dbpath"
          - /data/db
          - "--bind_ip"
          - 0.0.0.0
          - "--port"
          - "27018"
        ports:
        - containerPort: 27018
        volumeMounts:
        - name: mongodb-shard1-data
          mountPath: /data/db
  volumeClaimTemplates:
  - metadata:
      name: mongodb-shard1-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: mongodb-storage
      resources:
        requests:
          storage: 1Gi