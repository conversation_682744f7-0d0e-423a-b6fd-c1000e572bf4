{"cells": [{"cell_type": "code", "execution_count": 1, "id": "3f07649d", "metadata": {}, "outputs": [], "source": ["from pymongo import MongoClient\n", "from kubernetes import client, config\n", "from pprint import pprint\n", "from kubernetes.client.rest import ApiException"]}, {"cell_type": "code", "execution_count": 2, "id": "d96b76ac", "metadata": {}, "outputs": [], "source": ["MONGO_HOST = '***************'\n", "MONGO_PORT = 32017\n", "NAMESPACE = 'default'\n", "K8S_SHARDS = 0\n", "\n", "\n", "# Load local kubeconfig (e.g., ~/.kube/config)\n", "config.load_kube_config()\n", "\n", "# Create API client for core Kubernetes resources\n", "apps_V1 = client.AppsV1Api()\n", "core_V1 = client.CoreV1Api()"]}, {"cell_type": "code", "execution_count": 3, "id": "85e2b65b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📋 Listing all pods and their statuses:\n", "\n", " - mongodb-configsvr-0: Running\n", " - mongodb-mongos-85d8b6979b-4r8xb: Running\n", " - mongodb-mongos-85d8b6979b-dvtx9: Running\n", " - mongodb-shard1-0: Running\n", " - mongodb-shard2-0: Running\n", " - mongodb-shard3-0: Running\n", " - mongodb-shard4-0: Running\n", " - mongodb-shard5-0: Running\n", " - mongodb-shard6-0: Running\n", "\n", " Currently there are 6 shards in k8s\n"]}], "source": ["def connect_to_kubernetes():\n", "    global K8S_SHARDS\n", "    K8S_SHARDS = 0\n", "    try:\n", "        print(\"📋 Listing all pods and their statuses:\\n\")\n", "\n", "        pods = core_V1.list_namespaced_pod(namespace=NAMESPACE,watch=False)\n", "        for pod in pods.items:\n", "            name = pod.metadata.name\n", "            status = pod.status.phase\n", "            if \"mongodb-shard\" in name:\n", "                K8S_SHARDS += 1\n", "            print(f\" - {name}: {status}\")\n", "        print(f\"\\n Currently there are {K8S_SHARDS} shards in k8s\")\n", "\n", "    except ApiException as e:\n", "        print(f\"❌ Kubernetes API error: {e}\")\n", "    except Exception as e:\n", "        print(f\"❌ Failed to connect to Kubernetes: {e}\")\n", "\n", "connect_to_kubernetes()"]}, {"cell_type": "code", "execution_count": 4, "id": "80b97ee7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Connected to MongoDB. Databases:\n", "['admin', 'config', 'ycsb_sharded']\n"]}], "source": ["# Replace with your mongos address and authentication if needed\n", "#mongos_uri = \"mongodb://***************:32017\"\n", "mongos_uri = \"mongodb://localhost:27017\"\n", "\n", "MONGO_SHARDS = 0\n", "\n", "# Connect to the mongos router\n", "mongoClient = MongoClient(mongos_uri)\n", "\n", "# Test connection by listing databases\n", "try:\n", "    print(\"Connected to MongoDB. Databases:\")\n", "    pprint(mongoClient.list_database_names())\n", "except Exception as e:\n", "    print(\"Connection failed:\", e)\n"]}, {"cell_type": "code", "execution_count": 5, "id": "f84e389d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📦 Current shards in the cluster: 6\n", " - shard1: shard1/mongodb-shard1-0.mongodb-shard1.default.svc.cluster.local:27018\n", " - shard2: shard2/mongodb-shard2-0.mongodb-shard2.default.svc.cluster.local:27018\n", " - shard3: shard3/mongodb-shard3-0.mongodb-shard3.default.svc.cluster.local:27018\n", " - shard4: shard4/mongodb-shard4-0.mongodb-shard4.default.svc.cluster.local:27018\n", " - shard5: shard5/mongodb-shard5-0.mongodb-shard5.default.svc.cluster.local:27018\n", " - shard6: shard6/mongodb-shard6-0.mongodb-shard6.default.svc.cluster.local:27018\n"]}], "source": ["def list_shards(mongos_client: MongoClient):\n", "    global MONGO_SHARDS\n", "    \"\"\"\n", "    Lists all current shards in the MongoDB sharded cluster.\n", "    \n", "    :param mongos_client: The MongoClient connected to mongos\n", "    \"\"\"\n", "    try:\n", "        result = mongos_client.admin.command(\"listShards\")\n", "        MONGO_SHARDS = len(result['shards'])\n", "        print(f\"📦 Current shards in the cluster: {MONGO_SHARDS}\")\n", "        for shard in result[\"shards\"]:\n", "            print(f\" - {shard['_id']}: {shard['host']}\")\n", "    except Exception as e:\n", "        print(f\"❌ Failed to list shards: {e}\")\n", "\n", "# List current shards\n", "list_shards(mongoClient)"]}, {"cell_type": "code", "execution_count": 7, "id": "f773e424", "metadata": {}, "outputs": [], "source": ["def remove_shard(mongos_client: MongoClient, shard_id: int):\n", "    \"\"\"\n", "    Initiates or continues removal of a shard from the MongoDB cluster.\n", "    This function executes the `removeShard` command only once, allowing\n", "    external control over when to reissue it.\n", "\n", "    :param mongos_client: The MongoClient connected to mongos\n", "    :param shard_id: Numeric string like '3' (for shard3)\n", "    \"\"\"\n", "    global MONGO_SHARDS\n", "    shard_name = f\"shard{shard_id}\"\n", "\n", "    try:\n", "        res = mongos_client.admin.command(\"removeShard\", shard_name)\n", "        state = res[\"state\"]\n", "        print(f\"📦 removeShard called on: {shard_name} (state: {state})\")\n", "        pprint(res)\n", "        if (state == 'completed'):\n", "            MONGO_SHARDS -= 1\n", "    except Exception as e:\n", "        print(f\"❌ Failed to remove shard {shard_name}: {e}\")\n", "\n", "\n", "def add_shard(mongos_client: MongoClient, shard_id: int):\n", "    \"\"\"\n", "    Adds a shard to the MongoDB cluster. Constructs shard URI from the shard ID.\n", "    \n", "    :param mongos_client: The MongoClient connected to mongos\n", "    :param shard_id: Numeric string like '3' (for shard3)\n", "    \"\"\"\n", "    global MONGO_SHARDS\n", "    shard_name = f\"shard{shard_id}\"\n", "    host = f\"mongodb-shard{shard_id}-0.mongodb-shard{shard_id}.default.svc.cluster.local:27018\"\n", "    shard_uri = f\"{shard_name}/{host}\"\n", "\n", "    try:\n", "        result = mongos_client.admin.command(\"addShard\", shard_uri)\n", "        print(f\"✅ Shard added: {shard_uri}\")\n", "        pprint(result)\n", "        MONGO_SHARDS += 1\n", "    except Exception as e:\n", "        print(f\"❌ Failed to add shard {shard_uri}: {e}\")\n", "\n", "\n", "def reshard_collection(mongos_client: MongoClient, db_name=\"ycsb_sharded\", coll_name=\"sharded\", chunks=40):\n", "    \"\"\"\n", "    Initiates a reshardCollection operation on the given collection with:\n", "    - forceRedistribution: true\n", "    - numInitialChunks: 40\n", "    - shard<PERSON>ey: { country: \"hashed\" }\n", "\n", "    :param mongos_client: MongoClient connected to mongos\n", "    :param db_name: Name of the database (e.g., \"ycsb_sharded\")\n", "    :param coll_name: Name of the collection (e.g., \"usertable\")\n", "    \"\"\"\n", "    numInitialChunks = chunks\n", "    while (numInitialChunks > 2*MONGO_SHARDS):\n", "        try:\n", "            namespace = f\"{db_name}.{coll_name}\"\n", "            cmd = {\n", "                \"reshardCollection\": namespace,\n", "                \"key\": { \"first_name\": \"hashed\" },\n", "                \"numInitialChunks\": numInitialChunks,\n", "                \"forceRedistribution\": True\n", "            }\n", "            print(f\"Resharding initiated for {namespace} with {numInitialChunks} chunks...✅\")\n", "            result = mongos_client.admin.command(cmd)\n", "            from pprint import pprint\n", "            pprint(result)\n", "            return True\n", "\n", "        except Exception as e:\n", "            #print(f\"Failed to reshard {namespace}:❌  \\n{e}\")\n", "            numInitialChunks -= 1\n", "    \n", "    return False\n", "\n", "from bson.int64 import Int64\n", "\n", "def split_hashed_chunks(mongos_client: MongoClient, db_name=\"ycsb_sharded\", coll_name=\"sharded\", num_chunks=40, sk_field=\"country\"):\n", "    \"\"\"\n", "    Splits a collection sharded on a hashed key into `num_chunks` chunks\n", "    using evenly distributed hashed key space split points.\n", "\n", "    :param mongos_client: MongoClient connected to mongos\n", "    :param db_name: Name of the database\n", "    :param coll_name: Name of the collection\n", "    :param num_chunks: Number of chunks to split into (default: 40)\n", "    \"\"\"\n", "    try:\n", "        print(f\"🔧 Splitting {db_name}.{coll_name} into {num_chunks} chunks on hashed {sk_field}...\")\n", "\n", "        ns = f\"{db_name}.{coll_name}\"\n", "        min_hash = -2**63\n", "        max_hash = 2**63\n", "\n", "        step = (max_hash - min_hash) // num_chunks\n", "        split_points = [Int64(min_hash + i * step) for i in range(1, num_chunks)]\n", "\n", "        for i, split_point in enumerate(split_points):\n", "            cmd = {\n", "                \"split\": ns,\n", "                f\"middle\": { sk_field: split_point }\n", "            }\n", "            res = mongos_client.admin.command(cmd)\n", "            print(f\"✅ Split {i+1}/{num_chunks - 1} at hashed value {split_point}\")\n", "\n", "        print(f\"🎉 Done splitting into {num_chunks} chunks.\")\n", "\n", "    except Exception as e:\n", "        print(f\"❌ Failed during split: {e}\")\n"]}, {"cell_type": "code", "execution_count": 8, "id": "3a1e82d5", "metadata": {}, "outputs": [], "source": ["def scale_k8s_shard_down(apps_v1, shard_id: int, namespace: str = \"default\"):\n", "    \"\"\"\n", "    Scales the StatefulSet for the given shard ID to 0 replicas.\n", "    \n", "    :param apps_v1: An initialized AppsV1Api client\n", "    :param shard_id: Integer ID of the shard (e.g., 3 for mongodb-shard3)\n", "    :param namespace: Kubernetes namespace (default: \"default\")\n", "    \"\"\"\n", "    global K8S_SHARDS\n", "    shard_name = f\"mongodb-shard{shard_id}\"\n", "    try:\n", "        apps_v1.patch_namespaced_stateful_set_scale(\n", "            name=shard_name,\n", "            namespace=namespace,\n", "            body={\"spec\": {\"replicas\": 0}}\n", "        )\n", "        print(f\"🔻 Scaled {shard_name} to 0 replicas.\")\n", "        K8S_SHARDS -= 1\n", "    except client.exceptions.ApiException as e:\n", "        print(f\"❌ Failed to scale down {shard_name}: {e}\")\n", "\n", "\n", "def scale_k8s_shard_up(apps_v1, shard_id: int, namespace: str = \"default\"):\n", "    \"\"\"\n", "    Scales the StatefulSet for the given shard ID to 1 replica.\n", "    \n", "    :param apps_v1: An initialized AppsV1Api client\n", "    :param shard_id: Integer ID of the shard (e.g., 3 for mongodb-shard3)\n", "    :param namespace: Kubernetes namespace (default: \"default\")\n", "    \"\"\"\n", "    global K8S_SHARDS\n", "    shard_name = f\"mongodb-shard{shard_id}\"\n", "    try:\n", "        apps_v1.patch_namespaced_stateful_set_scale(\n", "            name=shard_name,\n", "            namespace=namespace,\n", "            body={\"spec\": {\"replicas\": 1}}\n", "        )\n", "        print(f\"🔺 Scaled {shard_name} to 1 replica.\")\n", "        K8S_SHARDS += 1\n", "    except client.exceptions.ApiException as e:\n", "        print(f\"❌ Failed to scale up {shard_name}: {e}\")\n", "\n", "\n", "def print_statefulsets_info(apps_v1: client.AppsV1Api, namespace: str = \"default\"):\n", "    \"\"\"\n", "    Prints information about all StatefulSets in the specified namespace.\n", "\n", "    :param apps_v1: Initialized AppsV1Api client\n", "    :param namespace: Kubernetes namespace (default: \"default\")\n", "    \"\"\"\n", "    try:\n", "        statefulsets = apps_v1.list_namespaced_stateful_set(namespace=namespace)\n", "        if not statefulsets.items:\n", "            print(f\"ℹ️ No StatefulSets found in namespace '{namespace}'.\")\n", "            return\n", "\n", "        print(f\"📦 StatefulSets in namespace '{namespace}':\\n\")\n", "        for sts in statefulsets.items:\n", "            name = sts.metadata.name\n", "            replicas = sts.spec.replicas\n", "            ready_replicas = sts.status.ready_replicas or 0\n", "            selector = sts.spec.selector.match_labels\n", "            print(f\" - {name}\")\n", "            print(f\"    🔢 Replicas: {ready_replicas}/{replicas}\")\n", "            print(f\"    🏷️ Selector: {selector}\\n\")\n", "\n", "    except client.exceptions.ApiException as e:\n", "        print(f\"❌ Kubernetes API error: {e}\")\n", "    except Exception as e:\n", "        print(f\"❌ Failed to retrieve StatefulSets: {e}\")\n"]}, {"cell_type": "code", "execution_count": 9, "id": "92e3381d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📦 StatefulSets in namespace 'default':\n", "\n", " - mongodb-configsvr\n", "    🔢 Replicas: 1/1\n", "    🏷️ Selector: {'app': 'mongodb-configsvr'}\n", "\n", " - mongodb-shard1\n", "    🔢 Replicas: 1/1\n", "    🏷️ Selector: {'app': 'mongodb-shard1'}\n", "\n", " - mongodb-shard2\n", "    🔢 Replicas: 1/1\n", "    🏷️ Selector: {'app': 'mongodb-shard2'}\n", "\n", " - mongodb-shard3\n", "    🔢 Replicas: 1/1\n", "    🏷️ Selector: {'app': 'mongodb-shard3'}\n", "\n", " - mongodb-shard4\n", "    🔢 Replicas: 1/1\n", "    🏷️ Selector: {'app': 'mongodb-shard4'}\n", "\n", " - mongodb-shard5\n", "    🔢 Replicas: 1/1\n", "    🏷️ Selector: {'app': 'mongodb-shard5'}\n", "\n", " - mongodb-shard6\n", "    🔢 Replicas: 1/1\n", "    🏷️ Selector: {'app': 'mongodb-shard6'}\n", "\n"]}], "source": ["print_statefulsets_info(apps_V1)"]}, {"cell_type": "code", "execution_count": 10, "id": "30844f2d", "metadata": {"vscode": {"languageId": "markdown"}}, "outputs": [{"ename": "SyntaxError", "evalue": "invalid syntax (2175669829.py, line 1)", "output_type": "error", "traceback": ["  \u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[10]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[31m    \u001b[39m\u001b[31m**Always scale down MongoDB before Kubernetes and scale up Kubernetes before MongoDB**\u001b[39m\n    ^\n\u001b[31mSyntaxError\u001b[39m\u001b[31m:\u001b[39m invalid syntax\n"]}], "source": ["**Always scale down MongoDB before Kubernetes and scale up Kubernetes before MongoDB**"]}, {"cell_type": "code", "execution_count": 13, "id": "377af004", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔻 Scaled mongodb-shard6 to 0 replicas.\n"]}], "source": ["scale_k8s_shard_down(apps_V1, 6)"]}, {"cell_type": "code", "execution_count": 10, "id": "1a49014a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔺 Scaled mongodb-shard4 to 1 replica.\n"]}], "source": ["scale_k8s_shard_up(apps_V1, 4)"]}, {"cell_type": "code", "execution_count": 13, "id": "ddd95680", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Shard added: shard6/mongodb-shard6-0.mongodb-shard6.default.svc.cluster.local:27018\n", "{'$clusterTime': {'clusterTime': Timestamp(1748437228, 18),\n", "                  'signature': {'hash': b'\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00'\n", "                                        b'\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00'\n", "                                        b'\\x00\\x00\\x00\\x00',\n", "                                'keyId': 0}},\n", " 'ok': 1.0,\n", " 'operationTime': Timestamp(1748437228, 18),\n", " 'shardAdded': 'shard6'}\n"]}], "source": ["add_shard(mongoClient, 6)"]}, {"cell_type": "code", "execution_count": 15, "id": "66d64e5c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📦 removeShard called on: shard6 (state: started)\n", "{'$clusterTime': {'clusterTime': Timestamp(1748504099, 2),\n", "                  'signature': {'hash': b'\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00'\n", "                                        b'\\x00\\x00\\x00\\x00\\x00\\x00\\x00\\x00'\n", "                                        b'\\x00\\x00\\x00\\x00',\n", "                                'keyId': 0}},\n", " 'dbsToMove': [],\n", " 'msg': 'draining started successfully',\n", " 'note': 'you need to drop or movePrimary these databases',\n", " 'ok': 1.0,\n", " 'operationTime': Timestamp(1748504099, 2),\n", " 'shard': 'shard6',\n", " 'state': 'started'}\n"]}], "source": ["remove_shard(mongoClient, 6)"]}, {"cell_type": "code", "execution_count": null, "id": "e366b822", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Resharding initiated for ycsb_sharded.usertable with 100 chunks...✅\n"]}], "source": ["reshard_collection(mongoClient, coll_name=\"usertable\", chunks=100)"]}, {"cell_type": "code", "execution_count": null, "id": "8762ed48", "metadata": {}, "outputs": [], "source": ["split_hashed_chunks(mongoClient, coll_name=\"usertable\", num_chunks=100, sk_field=\"first_name\")"]}, {"cell_type": "code", "execution_count": null, "id": "33c882c9", "metadata": {}, "outputs": [], "source": ["def same_replicas_number():\n", "    global MONGO_SHARDS, K8S_SHARDS\n", "    if (MONGO_SHARDS == K8S_SHARDS):\n", "        print(f\"The number of replicas in Kubernetes and MongoDB is the same: {K8S_SHARDS}\")\n", "        return True\n", "    else:\n", "        print(\"The number of replicas is different:\")\n", "        print(f\"- {K8S_SHARDS} in Kubernetes\")\n", "        print(f\"- {MONGO_SHARDS} in MongoDB\")\n", "        return False"]}, {"cell_type": "code", "execution_count": null, "id": "48b96a22", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The number of replicas in Kubernetes and MongoDB is the same: 6\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["same_replicas_number()"]}, {"cell_type": "code", "execution_count": null, "id": "afd9c9e3", "metadata": {}, "outputs": [], "source": ["def scale_cluster_down(mongoClient, apps_V1):\n", "    if (same_replicas_number == False):\n", "        print(\"❌Can't scale down because replicas number is different!\")\n", "        same_replicas_number()\n", "        return False\n", "    else:\n", "        print(f\"Starting scaling down from {MONGO_SHARDS} to {MONGO_SHARDS - 1} replicas...\\n\")\n", "        remove_shard(mongoClient, MONGO_SHARDS)\n", "        print(\"Resharding the collection...\\n\")\n", "        reshard_collection(mongoClient, coll_name=\"usertable\", chunks=100, shard_key_field=\"first_name\")\n", "        remove_shard(mongoClient, MONGO_SHARDS)\n", "        scale_k8s_shard_down(apps_V1, K8S_SHARDS)\n", "        print(\"✅Scaled down succesfully!\")\n", "        same_replicas_number()\n", "        return True\n", "\n", "def scale_cluster_up(mongoClient, apps_V1):\n", "    if (same_replicas_number == False):\n", "        print(\"❌Can't scale up because replicas number is different!\")\n", "        same_replicas_number()\n", "        return False\n", "    else:\n", "        print(f\"Starting scaling up from {K8S_SHARDS} to {K8S_SHARDS + 1} replicas...\\n\")\n", "        scale_k8s_shard_up(apps_V1, K8S_SHARDS + 1)\n", "        add_shard(mongoClient, K8S_SHARDS)\n", "        print(\"Resharding the collection...\\n\")\n", "        reshard_collection(mongoClient, coll_name=\"usertable\", chunks=100, shard_key_field=\"first_name\")\n", "        print(\"✅Scaled up succesfully!\")\n", "        same_replicas_number()\n", "        return True\n", "        "]}, {"cell_type": "code", "execution_count": null, "id": "a4bd7261", "metadata": {}, "outputs": [], "source": ["scale_cluster_down(mongoClient, apps_V1)"]}, {"cell_type": "code", "execution_count": null, "id": "18b5117d", "metadata": {}, "outputs": [], "source": ["scale_cluster_up(mongoClient, apps_V1)"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}