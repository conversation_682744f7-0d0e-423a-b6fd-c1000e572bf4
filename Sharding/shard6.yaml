apiVersion: v1
kind: Service
metadata:
  name: mongodb-shard6
spec:
  selector:
    app: mongodb-shard6
  clusterIP: None
  ports:
  - port: 27018
    targetPort: 27018
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mongodb-shard6
spec:
  serviceName: mongodb-shard6
  replicas: 0
  selector:
    matchLabels:
      app: mongodb-shard6
  template:
    metadata:
      labels:
        app: mongodb-shard6
    spec:
      containers:
      - name: mongodb-shard6
        image: mongo:8.0
        command:
          - mongod
          - "--shardsvr"
          - "--replSet"
          - shard6
          - "--dbpath"
          - /data/db
          - "--bind_ip"
          - 0.0.0.0
          - "--port"
          - "27018"
        ports:
        - containerPort: 27018
        volumeMounts:
        - name: mongodb-shard6-data
          mountPath: /data/db
  volumeClaimTemplates:
  - metadata:
      name: mongodb-shard6-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: mongodb-storage
      resources:
        requests:
          storage: 1Gi