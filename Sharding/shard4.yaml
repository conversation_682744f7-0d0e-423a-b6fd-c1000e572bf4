apiVersion: v1
kind: Service
metadata:
  name: mongodb-shard4
spec:
  selector:
    app: mongodb-shard4
  clusterIP: None
  ports:
  - port: 27018
    targetPort: 27018
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: mongodb-shard4
spec:
  serviceName: mongodb-shard4
  replicas: 0
  selector:
    matchLabels:
      app: mongodb-shard4
  template:
    metadata:
      labels:
        app: mongodb-shard4
    spec:
      containers:
      - name: mongodb-shard4
        image: mongo:8.0
        command:
          - mongod
          - "--shardsvr"
          - "--replSet"
          - shard4
          - "--dbpath"
          - /data/db
          - "--bind_ip"
          - 0.0.0.0
          - "--port"
          - "27018"
        ports:
        - containerPort: 27018
        volumeMounts:
        - name: mongodb-shard4-data
          mountPath: /data/db
  volumeClaimTemplates:
  - metadata:
      name: mongodb-shard4-data
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: mongodb-storage
      resources:
        requests:
          storage: 1Gi