apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-frontend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nginx-frontend
  template:
    metadata:
      labels:
        app: nginx-frontend
    spec:
      containers:
      - name: nginx-frontend
        image: nginx-frontend:latest
        imagePullPolicy: Never 
        ports:
        - containerPort: 80
        resources:
            limits:
              memory: "512Mi"
              cpu: "500m"
            requests:
              memory: "256Mi"
              cpu: "250m"
---
apiVersion: v1
kind: Service
metadata:
  name: nginx-frontend
spec:
  selector:
    app: nginx-frontend
  ports:
    - protocol: TCP
      port: 80
      targetPort: 80
  type: ClusterIP  # Or LoadBalancer if you need external access