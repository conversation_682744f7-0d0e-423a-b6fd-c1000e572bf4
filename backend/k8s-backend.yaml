apiVersion: apps/v1
kind: Deployment
metadata:
  name: flask-backend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: flask-backend
  template:
    metadata:
      labels:
        app: flask-backend
    spec:
      containers:
        - name: flask-backend
          image: flask-backend:latest
          imagePullPolicy: Never
          ports:
            - containerPort: 5000
          env:
            - name: MONGO_URI
              value: "mongodb://mongo:27017/testdb"
          resources:
            limits:
              memory: "512Mi"
              cpu: "500m"
            requests:
              memory: "256Mi"
              cpu: "250m"

---
apiVersion: v1
kind: Service
metadata:
  name: flask-backend
spec:
  selector:
    app: flask-backend
  ports:
    - protocol: TCP
      port: 5000
      targetPort: 5000
  type: LoadBalancer
