{"cells": [{"cell_type": "code", "execution_count": null, "id": "4694e41b", "metadata": {}, "outputs": [], "source": ["\n", "# LSTM Time Series Prediction for Max Concurrent Tasks\n", "# Predicting max_concurrent_tasks using PyTorch LSTM\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import matplotlib.dates as mdates\n", "import seaborn as sns\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# PyTorch imports\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "from torch.utils.data import Dataset, DataLoader\n", "from sklearn.preprocessing import MinMaxScaler\n", "from sklearn.metrics import mean_squared_error, mean_absolute_error\n", "\n", "# Set style for better plots\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Libraries imported successfully!\")\n", "print(f\"PyTorch version: {torch.__version__}\")\n", "print(f\"CUDA available: {torch.cuda.is_available()}\")"]}, {"cell_type": "code", "execution_count": null, "id": "9d8b18d3", "metadata": {}, "outputs": [], "source": ["print(\"\\n\" + \"=\"*60)\n", "print(\"STEP 1: DATA LOADING AND PREPROCESSING\")\n", "print(\"=\"*60)\n", "\n", "# Load the dataset\n", "df = pd.read_csv('./datasets/askalon_ee_trace_processed.csv', sep=',')\n", "\n", "print(f\"Dataset shape: {df.shape}\")\n", "print(f\"Columns: {list(df.columns)}\")\n", "\n", "# Sort by timestamp to ensure proper time series order\n", "df['ts_submit_dt2'] = pd.to_datetime(df['ts_submit_dt2'])\n", "df = df.sort_values('ts_submit_dt2').reset_index(drop=True)\n", "\n", "print(f\"\\nTime range: {df['ts_submit_dt'].min()} to {df['ts_submit_dt'].max()}\")\n", "print(f\"Max concurrent tasks range: {df['max_concurrent_tasks'].min()} to {df['max_concurrent_tasks'].max()}\")\n", "\n", "# Basic statistics\n", "print(\"\\nDataset Statistics:\")\n", "print(df[['max_concurrent_tasks', 'total_queries_count', 'aggregation_queries_count']].describe())"]}, {"cell_type": "code", "execution_count": null, "id": "0fb8441d", "metadata": {}, "outputs": [], "source": ["print(\"\\n\" + \"=\"*60)\n", "print(\"STEP 2: EXPLORATORY DATA ANALYSIS\")\n", "print(\"=\"*60)\n", "\n", "# Create visualizations\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# Plot 1: Max concurrent tasks over time\n", "step = len(df) // 10  # Adjust 10 to the number of ticks you want\n", "xticks = df['ts_submit_dt2'][::step]\n", "\n", "axes[0, 0].plot(df['ts_submit_dt2'], df['max_concurrent_tasks'], marker='o', linewidth=2, markersize=1)\n", "axes[0, 0].set_title('Max Concurrent Tasks Over Time')\n", "axes[0, 0].set_xlabel('Timestamp')\n", "axes[0, 0].set_ylabel('Max Concurrent Tasks')\n", "axes[0, 0].xaxis.set_major_locator(mdates.AutoDateLocator(maxticks=10))\n", "axes[0, 0].xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))\n", "axes[0, 0].tick_params(axis='x', rotation=45)\n", "\n", "\n", "# Plot 2: Distribution of max concurrent tasks\n", "axes[0, 1].hist(df['max_concurrent_tasks'], bins=20, alpha=0.7, edgecolor='black')\n", "axes[0, 1].set_title('Distribution of Max Concurrent Tasks')\n", "axes[0, 1].set_xlabel('Max Concurrent Tasks')\n", "axes[0, 1].set_ylabel('Frequency')\n", "\n", "# Plot 3: Correlation with other features\n", "corr_data = df[['max_concurrent_tasks', 'total_queries_count', 'aggregation_queries_count']].corr()\n", "sns.heatmap(corr_data, annot=True, cmap='coolwarm', center=0, ax=axes[1, 0])\n", "axes[1, 0].set_title('Feature Correlation Matrix')\n", "\n", "# Plot 4: Max concurrent tasks vs total queries\n", "axes[1, 1].scatter(df['total_queries_count'], df['max_concurrent_tasks'], alpha=0.6)\n", "axes[1, 1].set_xlabel('Total Queries Count')\n", "axes[1, 1].set_ylabel('Max Concurrent Tasks')\n", "axes[1, 1].set_title('Total Queries vs Max Concurrent Tasks')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "b2d039b1", "metadata": {}, "outputs": [], "source": ["print(\"\\n\" + \"=\"*60)\n", "print(\"STEP 3: DATA PREPARATION FOR LSTM\")\n", "print(\"=\"*60)\n", "\n", "# Extract the target variable - changed to max_concurrent_tasks\n", "data = df['max_concurrent_tasks'].values.reshape(-1, 1)\n", "\n", "# Normalize the data\n", "scaler = MinMaxScaler(feature_range=(0, 1))\n", "scaled_data = scaler.fit_transform(data)\n", "\n", "# Create sequences for LSTM\n", "def create_sequences(data, seq_length):\n", "    X, y = [], []\n", "    for i in range(len(data) - seq_length):\n", "        #Input sequence\n", "        X.append(data[i:(i+seq_length)])\n", "        #Target value\n", "        y.append(data[i+seq_length])\n", "    return np.array(X), np.array(y)\n", "\n", "# Define sequence length (how many previous time steps to use for prediction)\n", "SEQUENCE_LENGTH = 8  # Use last X time steps to predict the next one\n", "\n", "X, y = create_sequences(scaled_data, SEQUENCE_LENGTH)\n", "\n", "print(f\"Input sequences shape: {X.shape}\")\n", "print(f\"Target values shape: {y.shape}\")\n", "print(f\"Each input sequence has {SEQUENCE_LENGTH} time steps\")\n", "\n", "# Split data into train and test sets\n", "train_size = int(len(X) * 0.8)\n", "X_train, X_test = X[:train_size], X[train_size:]\n", "y_train, y_test = y[:train_size], y[train_size:]\n", "\n", "print(f\"\\nTraining set: {X_train.shape[0]} samples\")\n", "print(f\"Test set: {X_test.shape[0]} samples\")"]}, {"cell_type": "code", "execution_count": null, "id": "4bfb0819", "metadata": {}, "outputs": [], "source": ["print(\"\\n\" + \"=\"*60)\n", "print(\"STEP 4: PY<PERSON><PERSON><PERSON> DATASET AND DATALOADER\")\n", "print(\"=\"*60)\n", "\n", "class TimeSeriesDataset(Dataset):\n", "    def __init__(self, X, y):\n", "        self.X = torch.FloatTensor(X)\n", "        self.y = torch.FloatTensor(y)\n", "    \n", "    def __len__(self):\n", "        return len(self.X)\n", "    \n", "    def __getitem__(self, idx):\n", "        return self.X[idx], self.y[idx]\n", "\n", "#Create datasets\n", "train_dataset = TimeSeriesDataset(X_train, y_train)\n", "test_dataset = TimeSeriesDataset(X_test, y_test)\n", "\n", "#Create dataloaders\n", "BATCH_SIZE = 32\n", "train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True)\n", "test_loader = DataLoader(test_dataset, batch_size=BATCH_SIZE, shuffle=False)\n", "\n", "print(f\"Training batches: {len(train_loader)}\")\n", "print(f\"Test batches: {len(test_loader)}\")\n", "print(f\"Batch size: {BATCH_SIZE}\")"]}, {"cell_type": "code", "execution_count": null, "id": "f73c55e1", "metadata": {}, "outputs": [], "source": ["print(\"\\n\" + \"=\"*60)\n", "print(\"STEP 5: LSTM MODEL DEFINITION\")\n", "print(\"=\"*60)\n", "\n", "class LSTMModel(nn.Module):\n", "    def __init__(self, input_size=1, hidden_size=50, num_layers=2, output_size=1, dropout=0.2):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "\n", "        self.hidden_size = hidden_size\n", "        self.num_layers = num_layers\n", "\n", "        self.lstm = nn.LSTM(\n", "            input_size = input_size,\n", "            hidden_size = hidden_size,\n", "            num_layers = num_layers,\n", "            batch_first = True,\n", "            dropout = dropout\n", "        )\n", "\n", "        self.linear = nn.Linear(hidden_size, output_size)\n", "    \n", "    def forward(self, x):\n", "        #initialize hidden state\n", "        batch_size = x.size(0)\n", "        h0 = torch.zeros(self.num_layers, batch_size, self.hidden_size)\n", "        c0 = torch.zeros(self.num_layers, batch_size, self.hidden_size)\n", "\n", "        #LSTM forward pass\n", "        lstm_out, _ = self.lstm(x, (h0, c0))\n", "\n", "        #Get last output\n", "        last_output = lstm_out[:, -1, :]\n", "\n", "        #Linear layer\n", "        predictions = self.linear(last_output)\n", "        return predictions\n", "\n", "#Model parameters\n", "INPUT_SIZE = 1      #Number of features (just 1, max_concurrent_tasks)\n", "HIDDEN_SIZE = 64    #Number of LSTM units\n", "NUM_LAYERS = 2      #Number of LSTM layers\n", "OUTPUT_SIZE = 1     #Number of output features (predicting max_concurrent_tasks)\n", "\n", "#Create Model\n", "model = LSTMModel(\n", "    input_size = INPUT_SIZE,\n", "    hidden_size = HIDDEN_SIZE,\n", "    num_layers = NUM_LAYERS,\n", "    output_size = OUTPUT_SIZE\n", ")\n", "\n", "print(f\"Model created successfully!\")\n", "print(f\"Model parameters:\")\n", "print(f\"- Input size: {INPUT_SIZE}\")\n", "print(f\"- Hidden size: {HIDDEN_SIZE}\")\n", "print(f\"- Number of layers: {NUM_LAYERS}\")\n", "print(f\"- Output size: {OUTPUT_SIZE}\")\n", "\n", "# Count parameters\n", "total_params = sum(p.numel() for p in model.parameters())\n", "trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)\n", "print(f\"- Total parameters: {total_params}\")\n", "print(f\"- Trainable parameters: {trainable_params}\")"]}, {"cell_type": "code", "execution_count": null, "id": "f1b72907", "metadata": {}, "outputs": [], "source": ["print(\"\\n\" + \"=\"*60)\n", "print(\"STEP 6: TRAINING SETUP\")\n", "print(\"=\"*60)\n", "\n", "# Loss function and optimizer\n", "criterion = nn.MS<PERSON><PERSON>()\n", "optimizer = optim.<PERSON>(model.parameters(), lr=0.001)\n", "\n", "# Training parameters\n", "NUM_EPOCHS = 50\n", "PRINT_EVERY = 10\n", "\n", "print(f\"Training configuration:\")\n", "print(f\"- Loss function: MSE\")\n", "print(f\"- Optimizer: <PERSON>\")\n", "print(f\"- Learning rate: 0.001\")\n", "print(f\"- Number of epochs: {NUM_EPOCHS}\")"]}, {"cell_type": "code", "execution_count": null, "id": "b6eb2337", "metadata": {}, "outputs": [], "source": ["print(\"\\n\" + \"=\"*60)\n", "print(\"STEP 7: <PERSON><PERSON><PERSON> TRAINING WITH EARLY STOPPING\")\n", "print(\"=\"*60)\n", "\n", "# Training loop with early stopping\n", "train_losses = []\n", "val_losses = []\n", "\n", "best_val_loss = float('inf')\n", "patience = 20           # Stop if no improvement after 10 epochs\n", "counter = 0\n", "early_stop = False\n", "\n", "# To restore the best model later\n", "best_model_state = None\n", "\n", "model.train()\n", "for epoch in range(NUM_EPOCHS):\n", "    if early_stop:\n", "        print(f\"\\nEarly stopping triggered at epoch {epoch+1}: Best validation loss: {best_val_loss:.6f}\")\n", "        break\n", "\n", "    # Training phase\n", "    train_loss = 0.0\n", "    for batch_X, batch_y in train_loader:\n", "        outputs = model(batch_X)\n", "        loss = criterion(outputs.squeeze(), batch_y.squeeze())\n", "\n", "        optimizer.zero_grad()\n", "        loss.backward()\n", "        optimizer.step()\n", "        train_loss += loss.item()\n", "\n", "    # Validation phase\n", "    model.eval()\n", "    val_loss = 0.0\n", "    with torch.no_grad():\n", "        for batch_X, batch_y in test_loader:\n", "            outputs = model(batch_X)\n", "            loss = criterion(outputs.squeeze(), batch_y.squeeze())\n", "            val_loss += loss.item()\n", "\n", "    model.train()\n", "\n", "    # Calculate average losses\n", "    avg_train_loss = train_loss / len(train_loader)\n", "    avg_val_loss = val_loss / len(test_loader)\n", "\n", "    train_losses.append(avg_train_loss)\n", "    val_losses.append(avg_val_loss)\n", "\n", "    # Early stopping logic\n", "    if avg_val_loss < best_val_loss:\n", "        best_val_loss = avg_val_loss\n", "        best_model_state = model.state_dict()\n", "        counter = 0\n", "    else:\n", "        counter += 1\n", "        if counter >= patience:\n", "            early_stop = True\n", "\n", "    # Print progress\n", "    if (epoch + 1) % PRINT_EVERY == 0 or early_stop:\n", "        print(f\"Epoch {epoch+1}/{NUM_EPOCHS}, Train Loss: {avg_train_loss:.6f}, Val Loss: {avg_val_loss:.6f}\")\n", "\n", "# Restore best model\n", "if best_model_state is not None:\n", "    model.load_state_dict(best_model_state)\n", "\n", "print(\"\\nTraining completed!\")"]}, {"cell_type": "code", "execution_count": null, "id": "7b5d8bf0", "metadata": {}, "outputs": [], "source": ["print(\"\\n\" + \"=\"*60)\n", "print(\"STEP 8: TRAINING VISUALIZATION\")\n", "print(\"=\"*60)\n", "\n", "# Plot training and validation losses\n", "plt.figure(figsize=(12, 5))\n", "\n", "plt.plot(train_losses, label='Training Loss', linewidth=2)\n", "plt.plot(val_losses, label='Validation Loss', linewidth=2)\n", "plt.title('Training and Validation Loss')\n", "plt.xlabel('Epoch')\n", "plt.ylabel('Loss')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "755ca0cf", "metadata": {}, "outputs": [], "source": ["print(\"\\n\" + \"=\"*60)\n", "print(\"STEP 9: MAKING PREDICTIONS\")\n", "print(\"=\"*60)\n", "\n", "#Make predictions on test set\n", "model.eval()\n", "predictions = []\n", "actuals = []\n", "\n", "with torch.no_grad():\n", "    for batch_X, batch_y in test_loader:\n", "        outputs = model(batch_X)\n", "        predictions.extend(outputs.squeeze().numpy())\n", "        actuals.extend(batch_y.squeeze().numpy())\n", "\n", "predictions = np.array(predictions).reshape(-1, 1)\n", "actuals = np.array(actuals).reshape(-1, 1)\n", "\n", "#Inverse transform the predictions and actuals\n", "predictions_original = scaler.inverse_transform(predictions)\n", "actuals_original = scaler.inverse_transform(actuals)\n", "\n", "print(f\"Made predictions for {len(predictions)} samples in the test set\")\n", "\n", "#Calculate metrics\n", "mse = mean_squared_error(actuals_original, predictions_original)\n", "rmse = np.sqrt(mse)\n", "mae = mean_absolute_error(actuals_original, predictions_original)\n", "\n", "print(f\"\\nModel Performance Metrics:\")\n", "print(f\"- Mean Squared Error (MSE): {mse:.2f}\")\n", "print(f\"- Root Mean Squared Error (RMSE): {rmse:.2f}\")\n", "print(f\"- Mean Absolute Error (MAE): {mae:.2f}\")"]}, {"cell_type": "code", "execution_count": null, "id": "2c8b8ef8", "metadata": {}, "outputs": [], "source": ["print(\"\\n\" + \"=\"*60)\n", "print(\"STEP 10: RESULTS VISUALIZATION\")\n", "print(\"=\"*60)\n", "\n", "# Get the corresponding timestamps for test data\n", "test_start_idx = train_size + SEQUENCE_LENGTH\n", "test_timestamps = df['ts_submit_dt'].iloc[test_start_idx:test_start_idx + len(predictions)]\n", "\n", "# Create comprehensive visualization\n", "fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "\n", "# Plot 1: Predictions vs Actual (line plot)\n", "xticks = test_timestamps[::10]\n", "axes[0, 0].plot(test_timestamps, actuals_original, 'o-', label='Actual', linewidth=2, markersize=6)\n", "axes[0, 0].plot(test_timestamps, predictions_original, 's-', label='Predicted', linewidth=2, markersize=6)\n", "axes[0, 0].set_title('Predictions vs Actual Values Over Time')\n", "axes[0, 0].set_xlabel('Timestamp')\n", "axes[0, 0].set_ylabel('Max Concurrent Tasks')\n", "axes[0, 0].legend()\n", "axes[0, 0].xaxis.set_major_locator(mdates.AutoDateLocator(maxticks=10))\n", "axes[0, 0].xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))\n", "axes[0, 0].tick_params(axis='x', rotation=45)\n", "axes[0, 0].grid(True, alpha=0.3)\n", "\n", "# Plot 2: Scatter plot of predictions vs actual\n", "axes[0, 1].scatter(actuals_original, predictions_original, alpha=0.6, s=50)\n", "axes[0, 1].plot([actuals_original.min(), actuals_original.max()], \n", "               [actuals_original.min(), actuals_original.max()], 'r--', linewidth=2)\n", "axes[0, 1].set_xlabel('Actual Values')\n", "axes[0, 1].set_ylabel('Predicted Values')\n", "axes[0, 1].set_title('Predicted vs Actual (Scatter Plot)')\n", "axes[0, 1].grid(True, alpha=0.3)\n", "\n", "# Plot 3: Residuals (prediction errors)\n", "residuals = actuals_original - predictions_original\n", "axes[1, 0].scatter(range(len(residuals)), residuals, alpha=0.6, s=50)\n", "axes[1, 0].axhline(y=0, color='r', linestyle='--', linewidth=2)\n", "axes[1, 0].set_xlabel('Sample Index')\n", "axes[1, 0].set_ylabel('Residuals (Actual - Predicted)')\n", "axes[1, 0].set_title('Prediction Residuals')\n", "axes[1, 0].grid(True, alpha=0.3)\n", "\n", "# Plot 4: Residuals distribution\n", "axes[1, 1].hist(residuals, bins=15, alpha=0.7, edgecolor='black')\n", "axes[1, 1].set_xlabel('Residuals')\n", "axes[1, 1].set_ylabel('Frequency')\n", "axes[1, 1].set_title('Distribution of Residuals')\n", "axes[1, 1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "e06eea07", "metadata": {}, "outputs": [], "source": ["#Load and examine the new dataset\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"STEP 11: SAVING MODEL AND LOADING NEW DATASET\")\n", "print(\"=\"*60)\n", "\n", "torch.save(best_model_state, \"best_lstm_model_state.pth\")\n", "print(\"Best model state saved to 'best_lstm_model_state.pth'\")\n", "\n", "saved_model = LSTMModel(\n", "    input_size = INPUT_SIZE,\n", "    hidden_size = HIDDEN_SIZE,\n", "    num_layers = NUM_LAYERS,\n", "    output_size = OUTPUT_SIZE\n", ")\n", "\n", "saved_model.load_state_dict(torch.load(\"best_lstm_model_state.pth\"))\n", "saved_model.eval()\n", "\n", "new_df = pd.read_csv('./datasets/galaxy_trace_processed.csv', sep=';')\n", "plt.figure(figsize=(10, 6))\n", "plt.plot(new_df['ts_submit_dt'].sort_values(), new_df['max_concurrent_tasks'], marker='', linestyle='-')\n", "plt.xlabel('ts_submit_dt')\n", "plt.ylabel('max_concurrent_tasks')\n", "plt.title('max_concurrent_tasks vs ts_submit_dt')\n", "plt.grid(True)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "edba5bff", "metadata": {}, "outputs": [], "source": ["# Complete the notebook with testing on new data\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"STEP 12: TESTING SAVED MODEL ON NEW DATA\")\n", "print(\"=\"*60)\n", "\n", "# Load and examine the new dataset\n", "print(f\"New dataset shape: {new_df.shape}\")\n", "print(f\"New dataset columns: {list(new_df.columns)}\")\n", "\n", "# Check if the new dataset has the same structure\n", "if 'max_concurrent_tasks' not in new_df.columns:\n", "    print(\"ERROR: 'max_concurrent_tasks' column not found in new dataset!\")\n", "    print(\"Available columns:\", list(new_df.columns))\n", "else:\n", "    print(f\"Time range in new data: {new_df['ts_submit_dt'].min()} to {new_df['ts_submit_dt'].max()}\")\n", "    print(f\"Max concurrent tasks range: {new_df['max_concurrent_tasks'].min()} to {new_df['max_concurrent_tasks'].max()}\")\n", "\n", "# Sort by timestamp to ensure proper time series order\n", "new_df = new_df.sort_values('ts_submit_dt').reset_index(drop=True)\n", "\n", "print(\"\\nNew Dataset Statistics:\")\n", "print(new_df[['max_concurrent_tasks']].describe())"]}, {"cell_type": "code", "execution_count": null, "id": "5dd98603", "metadata": {}, "outputs": [], "source": ["print(\"\\n\" + \"=\"*60)\n", "print(\"STEP 13: PREPARE NEW DATA FOR PREDICTION\")\n", "print(\"=\"*60)\n", "\n", "# Extract the target variable from new data\n", "new_data = new_df['max_concurrent_tasks'].values.reshape(-1, 1)\n", "\n", "# IMPORTANT: Use the same scaler that was fitted on training data\n", "# We need to transform the new data using the same scaling parameters\n", "new_scaled_data = scaler.transform(new_data)\n", "\n", "print(f\"New data shape after scaling: {new_scaled_data.shape}\")\n", "\n", "# Create sequences for the new data\n", "# We need at least SEQUENCE_LENGTH points to start making predictions\n", "if len(new_scaled_data) < SEQUENCE_LENGTH:\n", "    print(f\"ERROR: New dataset has only {len(new_scaled_data)} points, but we need at least {SEQUENCE_LENGTH} points to make predictions!\")\n", "else:\n", "    new_X, new_y = create_sequences(new_scaled_data, SEQUENCE_LENGTH)\n", "    print(f\"New input sequences shape: {new_X.shape}\")\n", "    print(f\"New target values shape: {new_y.shape}\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "23d65fa8", "metadata": {}, "outputs": [], "source": ["print(\"\\n\" + \"=\"*60)\n", "print(\"STEP 14: MAKE PREDICTIONS ON NEW DATA\")\n", "print(\"=\"*60)\n", "\n", "# Make predictions using the saved model\n", "saved_model.eval()\n", "new_predictions = []\n", "new_actuals = []\n", "\n", "# Convert to tensors for prediction\n", "new_X_tensor = torch.FloatTensor(new_X)\n", "new_y_tensor = torch.FloatTensor(new_y)\n", "\n", "with torch.no_grad():\n", "    # Process in batches to handle memory efficiently\n", "    batch_size = 32\n", "    for i in range(0, len(new_X_tensor), batch_size):\n", "        batch_X = new_X_tensor[i:i+batch_size]\n", "        batch_y = new_y_tensor[i:i+batch_size]\n", "        \n", "        outputs = saved_model(batch_X)\n", "        new_predictions.extend(outputs.squeeze().numpy())\n", "        new_actuals.extend(batch_y.squeeze().numpy())\n", "\n", "new_predictions = np.array(new_predictions).reshape(-1, 1)\n", "new_actuals = np.array(new_actuals).reshape(-1, 1)\n", "\n", "# Inverse transform to get original scale\n", "new_predictions_original = scaler.inverse_transform(new_predictions)\n", "new_actuals_original = scaler.inverse_transform(new_actuals)\n", "\n", "print(f\"Made predictions for {len(new_predictions)} samples in the new dataset\")\n", "\n", "# Calculate metrics for new data\n", "new_mse = mean_squared_error(new_actuals_original, new_predictions_original)\n", "new_rmse = np.sqrt(new_mse)\n", "new_mae = mean_absolute_error(new_actuals_original, new_predictions_original)\n", "\n", "print(f\"\\nModel Performance on New Data:\")\n", "print(f\"- Mean Squared Error (MSE): {new_mse:.2f}\")\n", "print(f\"- Root Mean Squared Error (RMSE): {new_rmse:.2f}\")\n", "print(f\"- Mean Absolute Error (MAE): {new_mae:.2f}\")"]}, {"cell_type": "code", "execution_count": null, "id": "cfffe316", "metadata": {}, "outputs": [], "source": ["print(\"\\n\" + \"=\"*60)\n", "print(\"STEP 15: VISUALIZE PREDICTIONS ON NEW DATA\")\n", "print(\"=\"*60)\n", "\n", "# Get corresponding timestamps for new data predictions\n", "new_test_start_idx = SEQUENCE_LENGTH\n", "new_test_timestamps = new_df['ts_submit_dt'].iloc[new_test_start_idx:new_test_start_idx + len(new_predictions)]\n", "\n", "# Create visualization for new data predictions\n", "fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "\n", "# Plot 1: Predictions vs Actual for new data\n", "axes[0, 0].plot(new_test_timestamps, new_actuals_original, 'o-', label='Actual', linewidth=2, markersize=4)\n", "axes[0, 0].plot(new_test_timestamps, new_predictions_original, 's-', label='Predicted', linewidth=2, markersize=4)\n", "axes[0, 0].set_title('New Data: Predictions vs Actual Values Over Time')\n", "axes[0, 0].set_xlabel('Timestamp')\n", "axes[0, 0].set_ylabel('Max Concurrent Tasks')\n", "axes[0, 0].legend()\n", "axes[0, 0].xaxis.set_major_locator(mdates.AutoDateLocator(maxticks=10))\n", "axes[0, 0].xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))\n", "axes[0, 0].tick_params(axis='x', rotation=45)\n", "axes[0, 0].grid(True, alpha=0.3)\n", "\n", "# Plot 2: <PERSON><PERSON><PERSON> plot for new data\n", "axes[0, 1].scatter(new_actuals_original, new_predictions_original, alpha=0.6, s=50, color='green')\n", "axes[0, 1].plot([new_actuals_original.min(), new_actuals_original.max()], \n", "               [new_actuals_original.min(), new_actuals_original.max()], 'r--', linewidth=2)\n", "axes[0, 1].set_xlabel('Actual Values')\n", "axes[0, 1].set_ylabel('Predicted Values')\n", "axes[0, 1].set_title('New Data: Predicted vs Actual (Scatter Plot)')\n", "axes[0, 1].grid(True, alpha=0.3)\n", "\n", "# Plot 3: Residuals for new data\n", "new_residuals = new_actuals_original - new_predictions_original\n", "axes[1, 0].scatter(range(len(new_residuals)), new_residuals, alpha=0.6, s=50, color='orange')\n", "axes[1, 0].axhline(y=0, color='r', linestyle='--', linewidth=2)\n", "axes[1, 0].set_xlabel('Sample Index')\n", "axes[1, 0].set_ylabel('Residuals (Actual - Predicted)')\n", "axes[1, 0].set_title('New Data: Prediction Residuals')\n", "axes[1, 0].grid(True, alpha=0.3)\n", "\n", "# Plot 4: Comparison of model performance\n", "metrics_comparison = {\n", "    'Dataset': ['Original Test', 'New Data'],\n", "    'MSE': [mse, new_mse],\n", "    'RMSE': [rmse, new_rmse],\n", "    'MAE': [mae, new_mae]\n", "}\n", "\n", "x_pos = np.arange(len(metrics_comparison['Dataset']))\n", "width = 0.25\n", "\n", "axes[1, 1].bar(x_pos - width, metrics_comparison['MSE'], width, label='MSE', alpha=0.7)\n", "axes[1, 1].bar(x_pos, metrics_comparison['RMSE'], width, label='RMSE', alpha=0.7)\n", "axes[1, 1].bar(x_pos + width, metrics_comparison['MAE'], width, label='MAE', alpha=0.7)\n", "\n", "axes[1, 1].set_xlabel('Dataset')\n", "axes[1, 1].set_ylabel('Error Value')\n", "axes[1, 1].set_title('Model Performance Comparison')\n", "axes[1, 1].set_xticks(x_pos)\n", "axes[1, 1].set_xticklabels(metrics_comparison['Dataset'])\n", "axes[1, 1].legend()\n", "axes[1, 1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}