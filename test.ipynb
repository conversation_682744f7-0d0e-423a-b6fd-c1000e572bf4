import pandas as pd
import matplotlib.pyplot as plt
#df_P7 = pd.read_parquet('Pegasus_P7_parquet/workflows/schema-1.0/part.0.parquet')

df_P7 = df_P7.loc[:, ~(df_P7.nunique() == 1) | ~((df_P7 == 0) | (df_P7 == 0.0)).all()]
df_P7 = df_P7.drop(columns=['critical_path_length', 'nfrs', 'scheduler', 'total_resources'], axis=1, errors='ignore')
df_P7['ts_submit_dt'] = pd.to_datetime(df_P7['ts_submit'], unit='s')
df_P7.to_csv('Pegasus_P7.csv')

df = pd.read_parquet('Galaxy/workflows/schema-1.0/part.0.parquet')
df = df.loc[:, ~(df.nunique() == 1) | ~((df == 0) | (df == 0.0)).all()]
df = df.drop(columns=['critical_path_length', 'nfrs', 'scheduler', 'total_resources', 'total_memory_usage'], axis=1, errors='ignore')
df['ts_submit_dt'] = pd.to_datetime(df['ts_submit'], unit='ms')
df

# Calculate the current min and max timestamps
min_ts = df['ts_submit_dt'].min()
max_ts = df['ts_submit_dt'].max()

# Calculate the new max timestamp to be one hour after the min timestamp
new_min_ts = min_ts
new_max_ts = min_ts + pd.Timedelta(hours=1)

# Calculate the scaling factor
total_seconds = (max_ts - min_ts).total_seconds()
new_total_seconds = (new_max_ts - new_min_ts).total_seconds()

# Scale all timestamps proportionally
df['ts_submit_dt2'] = df['ts_submit_dt'].apply(
    lambda x: new_min_ts + pd.Timedelta(seconds=((x - min_ts).total_seconds() * new_total_seconds / total_seconds))
)
df.to_csv('Galaxy.csv')

plt.figure(figsize=(10, 6))
plt.plot(df_P7['ts_submit_dt'].sort_values(), df_P7['task_count'], marker='', linestyle='-')
plt.xlabel('ts_submit_dt')
plt.ylabel('task_count')
plt.title('Task Count vs ts_submit_dt')
plt.grid(True)
plt.show()

plt.figure(figsize=(10, 6))
plt.plot(df['ts_submit_dt'].sort_values(), df['task_count'], marker='', linestyle='-')
plt.xlabel('ts_submit_dt')
plt.ylabel('task_count')
plt.title('Task Count vs ts_submit_dt')
plt.grid(True)
plt.show()

df = pd.read_parquet('askalon_ee2_parquet/workflows/schema-1.0/part.0.parquet')
df = df.loc[:, ~(df.nunique() == 1) | ~((df == 0) | (df == 0.0)).all()]
df = df.drop(columns=['critical_path_length', 'nfrs', 'scheduler', 'total_resources', 'total_memory_usage'], axis=1, errors='ignore')
df['ts_submit_dt'] = pd.to_datetime(df['ts_submit'], unit='ms')
df

plt.figure(figsize=(10, 6))
plt.plot(df['ts_submit_dt'].sort_values(), df['task_count'], marker='', linestyle='-')
plt.xlabel('ts_submit_dt')
plt.ylabel('task_count')
plt.title('Task Count vs ts_submit_dt')
plt.grid(True)
plt.show()

df = pd.read_parquet('askalon_ee_parquet/workflows/schema-1.0/part.0.parquet')
df = df.loc[:, ~(df.nunique() == 1) | ~((df == 0) | (df == 0.0)).all()]
df = df.drop(columns=['critical_path_length', 'nfrs', 'scheduler', 'total_resources', 'total_memory_usage'], axis=1, errors='ignore')
df['ts_submit_dt'] = pd.to_datetime(df['ts_submit'], unit='ms')
df.to_csv('askalon_ee.csv')

plt.figure(figsize=(10, 6))
plt.plot(df['ts_submit_dt'].sort_values(), df['task_count'], marker='', linestyle='-')
plt.xlabel('ts_submit_dt')
plt.ylabel('task_count')
plt.title('Task Count vs ts_submit_dt')
plt.grid(True)
plt.show()

df = pd.read_parquet('LANL_Mustang_parquet/workflows/schema-1.0/part.0.parquet')
df = df.loc[:, ~(df.nunique() == 1) | ~((df == -1) | (df == -1.0)).all()]
df = df.drop(columns=['critical_path_length', 'nfrs', 'scheduler', 'total_resources', 'total_memory_usage'], axis=1, errors='ignore')
df['ts_submit_dt'] = pd.to_datetime(df['ts_submit'], unit='ms')
df['ts_submit_dt'] = df['ts_submit_dt'].sort_values()
df['total_queries_count'] = df['task_count']
df.drop(columns=['task_count'], inplace=True)
df.drop(columns=['id'], inplace=True)
df.set_index('ts_submit_dt', inplace=True)
df.to_csv('LANL_Mustang.csv')

plt.figure(figsize=(10, 6))
plt.plot(df['ts_submit_dt'].sort_values(), df['task_count'], marker='', linestyle='-')
plt.xlabel('ts_submit_dt')
plt.ylabel('task_count')
plt.title('Task Count vs ts_submit_dt')
plt.grid(True)
plt.show()

import pandas as pd

df = pd.read_csv('./datasets/galaxy_trace_smoothed.csv')
to_save = df.head()
to_save.to_csv('galaxy_trace_smoothed_head.csv')

df = pd.read_csv('./datasets/askalon_ee_trace.csv')
plt.figure(figsize=(10, 6))
plt.plot(df['ts_submit_dt'].sort_values(), df['total_queries_count']/10, marker='', linestyle='-')
plt.xlabel('ts_submit_dt')
plt.ylabel('total_queries_count')
plt.title('total_queries_count vs ts_submit_dt')
plt.show()

df = pd.read_csv('./datasets/galaxy_trace_processed.csv')
plt.figure(figsize=(10, 6))
plt.plot(df['ts_submit_dt'].sort_values(), df['total_queries_count']/10, marker='', linestyle='-')
plt.xlabel('ts_submit_dt')
plt.ylabel('total_queries_count')
plt.title('total_queries_count vs ts_submit_dt')
plt.show()

df = pd.read_csv('./datasets/askalon_ee_trace_smoothed.csv')
plt.figure(figsize=(10, 6))
plt.plot(df['ts_submit_dt'].sort_values(), df['max_concurrent_tasks']/10, marker='', linestyle='-')
plt.xlabel('ts_submit_dt')
plt.ylabel('max_concurrent_tasks')
plt.title('max_concurrent_tasks vs ts_submit_dt')
plt.show()

df = pd.read_csv('./askalon_ee_trace_concurrent_tasks_smoothed.csv')
plt.figure(figsize=(10, 6))
plt.plot(df['ts_submit_dt'].sort_values(), df['max_concurrent_tasks'], marker='', linestyle='-')
plt.xlabel('ts_submit_dt')
plt.ylabel('max_concurrent_tasks')
plt.title('max_concurrent_tasks vs ts_submit_dt')
plt.show()

df = pd.read_csv('./datasets/askalon_ee_trace_processed.csv')
plt.figure(figsize=(10, 6))
plt.plot(df['ts_submit_dt'].sort_values(), df['max_concurrent_tasks'], marker='', linestyle='-')
plt.xlabel('ts_submit_dt')
plt.ylabel('max_concurrent_tasks')
plt.title('max_concurrent_tasks vs ts_submit_dt')
plt.show()

import pandas as pd
import matplotlib.pyplot as plt

df = pd.read_csv('./datasets/askalon_ee_trace_processed.csv')
df = pd.read_csv('./datasets/askalon_ee_trace_processed.csv')
plt.figure(figsize=(10, 6))
plt.plot(df['ts_submit_dt'].sort_values(), df['max_concurrent_tasks'], marker='', linestyle='-')
plt.xlabel('ts_submit_dt')
plt.ylabel('max_concurrent_tasks')
plt.title('max_concurrent_tasks vs ts_submit_dt')
plt.show()

plt.figure(figsize=(12, 8))
for col in ['aggregation_queries_count', 'total_queries_count', 'standard_queries_count', 'first_name_queries_count', 'last_name_queries_count', 'country_queries_count', 'other_queries_count']:
    plt.plot(df['max_concurrent_tasks'], df[col], marker='o', linestyle='', label=col, alpha=0.6)
plt.xlabel('max_concurrent_tasks')
plt.ylabel('Count')
plt.title('Query Counts vs max_concurrent_tasks')
plt.legend()
plt.grid(True)
plt.show()

df = pd.read_csv('./datasets/galaxy_trace_processed.csv')
df.head()

plt.figure(figsize=(10, 6))
plt.plot(df['ts_submit_dt'].sort_values(), df['max_concurrent_tasks'], marker='', linestyle='-')
plt.xlabel('ts_submit_dt')
plt.ylabel('max_concurrent_tasks')
plt.title('max_concurrent_tasks vs ts_submit_dt')
plt.show()



df = pd.read_csv('./datasets/askalon_ee_trace_processed.csv', sep=';')
df2 = pd.read_csv('./datasets/galaxy_trace_processed.csv')

# Convert ts_submit_dt columns to datetime if not already
df['ts_submit_dt'] = pd.to_datetime(df['ts_submit_dt'])
df2['ts_submit_dt'] = pd.to_datetime(df2['ts_submit_dt'])

# Find the max ts_submit_dt in df
max_ts = df['ts_submit_dt'].max()

# Find the min ts_submit_dt in df2
min_ts_df2 = df2['ts_submit_dt'].min()

# Calculate the time difference to shift df2 so it starts after df
shift = (max_ts - min_ts_df2) + pd.Timedelta(seconds=1)

# Shift df2's ts_submit_dt
df2['ts_submit_dt'] = df2['ts_submit_dt'] + shift

# Concatenate and sort
df_concat = pd.concat([df, df2], ignore_index=True)
df_concat = df_concat.sort_values('ts_submit_dt')
df_concat

plt.figure(figsize=(10, 6))
plt.plot(df_concat['ts_submit_dt'].sort_values(), df_concat['max_concurrent_tasks'], marker='', linestyle='-')
plt.xlabel('ts_submit_dt')
plt.ylabel('max_concurrent_tasks')
plt.title('max_concurrent_tasks vs ts_submit_dt')
plt.show()

df_concat.head().to_csv('./datasets/combined.csv')