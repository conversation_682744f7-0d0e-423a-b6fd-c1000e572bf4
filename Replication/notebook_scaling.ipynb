{"cells": [{"cell_type": "code", "execution_count": 3, "id": "33bdfed7", "metadata": {}, "outputs": [], "source": ["import random\n", "import time\n", "from kubernetes import client, config\n", "from pymongo import MongoClient"]}, {"cell_type": "code", "execution_count": 30, "id": "e9380c91", "metadata": {}, "outputs": [], "source": ["# Load Kubernetes config (Minikube)\n", "config.load_kube_config()\n", "\n", "# Kubernetes API client\n", "v1_apps = client.AppsV1Api()\n", "v1_core = client.CoreV1Api()\n", "\n", "STATEFULSET_NAME = \"mongo\"\n", "NAMESPACE = \"default\""]}, {"cell_type": "code", "execution_count": 32, "id": "86512022", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Connected to MongoDB primary.\n"]}], "source": ["# Establish connection to MongoDB\n", "mongo_client = MongoClient(\"mongodb://localhost:27017/\", directConnection=True)\n", "admin_db = mongo_client.admin\n", "while admin_db.command(\"hello\")[\"isWritablePrimary\"] == False:\n", "    print(\"Waiting for connection to MongoDB primary...\")\n", "    mongo_client = MongoClient(\"mongodb://localhost:27017/\", directConnection=True)\n", "    admin_db = mongo_client.admin\n", "    time.sleep(1)\n", "if admin_db.command(\"hello\")[\"isWritablePrimary\"] == True:\n", "    print(\"Connected to MongoDB primary.\")"]}, {"cell_type": "code", "execution_count": 6, "id": "12c4f924", "metadata": {}, "outputs": [], "source": ["# Define global variables\n", "current_k8s_replicas = 0\n", "current_mongodb_replicas = 0\n", "\n", "DNS_ADDRESS = \".mongo.default.svc.cluster.local\"\n", "PORT = 27017\n", "\n", "SCALING_INTERVAL = 20 # seconds"]}, {"cell_type": "code", "execution_count": 13, "id": "5caf24aa", "metadata": {}, "outputs": [], "source": ["def get_replica_count():\n", "    \"\"\"Fetches the current number of replicas for the StatefulSet.\"\"\"\n", "    global current_k8s_replicas\n", "    statefulset = v1_apps.read_namespaced_stateful_set(STATEFULSET_NAME, NAMESPACE)\n", "    current_k8s_replicas = int(statefulset.spec.replicas)\n", "\n", "    return current_k8s_replicas\n", "\n", "def get_mongo_replica_count():\n", "    \"\"\"Fetches the number of members in the MongoDB replica set.\"\"\"\n", "    global current_mongodb_replicas\n", "    try:\n", "        response = admin_db.command(\"replSetGetStatus\")\n", "        members = response.get(\"members\", [])\n", "        current_mongodb_replicas = len(members)\n", "        return current_mongodb_replicas\n", "    except Exception as e:\n", "        print(f\"Error retrieving replica set status: {e}\")\n", "        return -1\n", "\n", "def scale_statefulset(new_replicas):\n", "    \"\"\"Scales the StatefulSet to the specified number of replicas.\"\"\"\n", "    global current_k8s_replicas\n", "    patch_body = {\"spec\": {\"replicas\": new_replicas}}\n", "    v1_apps.patch_namespaced_stateful_set_scale(\n", "        name=STATEFULSET_NAME, namespace=NAMESPACE, body=patch_body\n", "    )\n", "    current_k8s_replicas = new_replicas\n", "    print(f\"Scaled {STATEFULSET_NAME} to {new_replicas} replicas.\")\n", "\n", "def add_replica_to_mongo(new_replicas_count):\n", "    \"\"\"Adds a new replica to the MongoDB replica set.\"\"\"\n", "    global current_mongodb_replicas\n", "    new_member_config = {\n", "        \"_id\": current_mongodb_replicas,\n", "        \"host\": f\"mongo-{new_replicas_count - 1}{DNS_ADDRESS}:{PORT}\",\n", "    }\n", "    print(f\"Adding replica with _id: {new_member_config[\"_id\"]}\")\n", "    current_config = admin_db.command(\"replSetGetConfig\")[\"config\"]\n", "    try:\n", "        current_config[\"members\"].append(new_member_config)\n", "        current_config[\"version\"] += 1\n", "        reconfig_result = admin_db.command(\"replSetReconfig\", current_config)\n", "        current_mongodb_replicas += 1\n", "        print(f\"Added new replica with _id: {new_member_config[\"_id\"]}\\n\")\n", "    except Exception as e:\n", "        print(f\"Error adding new replica: {e}\")\n", "\n", "def remove_last_replica_from_mongo():\n", "    \"\"\"Removes the last replica from the MongoDB replica set.\"\"\"\n", "    global current_mongodb_replicas\n", "    try:\n", "        current_config = admin_db.command(\"replSetGetConfig\")[\"config\"]\n", "        members = current_config[\"members\"]\n", "\n", "        if len(members) <= 3:\n", "            print(\"Cannot remove the last replica. Minimum 3 replicas required.\")\n", "            return\n", "\n", "        # Get the _id of the last member\n", "        last_member_id = members[-1][\"_id\"]\n", "        last_member_host = members[-1][\"host\"]\n", "\n", "        print(f\"Attempting to remove member with _id: {last_member_id} and host: {last_member_host}\")\n", "\n", "        # Create a new configuration excluding the last member\n", "        new_members = [member for member in members if member[\"_id\"] != last_member_id]\n", "        current_config[\"members\"] = new_members\n", "        current_config[\"version\"] += 1\n", "\n", "        reconfig_result = admin_db.command(\"replSetReconfig\", current_config)\n", "        current_mongodb_replicas -= 1\n", "        print(f\"Removed member with _id: {last_member_id}\\n\")\n", "    except Exception as e:\n", "        print(f\"Error removing last replica: {e}\")\n", "\n", "current_k8s_replicas = get_replica_count()\n", "current_mongodb_replicas = get_mongo_replica_count()"]}, {"cell_type": "code", "execution_count": 8, "id": "011027a7", "metadata": {}, "outputs": [], "source": ["def scale_up(new_replicas_count):\n", "    print(f\"\\nScaling up to: {new_replicas_count} replicas...\\n\")\n", "\n", "    scale_statefulset(new_replicas_count)\n", "    add_replica_to_mongo(new_replicas_count)\n", "    return\n", "\n", "def scale_down(new_replicas_count):\n", "    print(f\"\\nScaling down to: {new_replicas_count} replicas...\\n\")\n", "\n", "    if new_replicas_count < 3:\n", "        print(\"Cannot scale down to less than 3 replicas.\\n\")\n", "        return\n", "    else:\n", "        scale_statefulset(new_replicas_count)\n", "        remove_last_replica_from_mongo()\n", "        return\n", "\n", "def auto_scale():\n", "    global current_k8s_replicas\n", "    global current_mongodb_replicas\n", "\n", "    current_k8s_replicas = get_replica_count()\n", "    print(f\"Starting with: {current_k8s_replicas} Kubernetes replicas\")\n", "\n", "    current_mongodb_replicas = get_mongo_replica_count()\n", "    print(f\"Starting with: {current_mongodb_replicas} MongoDB replicas\")\n", "    \n", "\n", "    while (current_k8s_replicas != current_mongodb_replicas):\n", "        print(\"Mismatch between Kubernetes and MongoDB replicas. Adjusting...\")\n", "        if (current_k8s_replicas > current_mongodb_replicas):\n", "            print(\"Scaling down statefulset...\")\n", "            difference = current_k8s_replicas - current_mongodb_replicas\n", "            new_k8s_replicas = current_k8s_replicas - difference\n", "            scale_statefulset(new_k8s_replicas) if new_k8s_replicas >= 3 else scale_statefulset(3)\n", "        elif (current_mongodb_replicas > current_k8s_replicas):\n", "            remove_last_replica_from_mongo()\n", "            print(\"Removing last replica from MongoDB...\")\n", "\n", "    else:\n", "        print(\"Kubernetes and MongoDB replica counts are matching.\")\n", "    \n", "    print(\"Starting auto-scaling process (press q to quit)...\\n\")\n", "\n", "    while True:\n", "\n", "        random_int = 0 #random.randint(0, 1)\n", "        print(f\"\\nRandom integer generated: {random_int}\")\n", "\n", "        if random_int == 0:\n", "            scale_down(current_k8s_replicas - 1)\n", "        elif random_int == 1:\n", "            scale_up(current_k8s_replicas + 1)\n", "\n", "        time.sleep(SCALING_INTERVAL)\n", "\n", "    return\n"]}, {"cell_type": "code", "execution_count": null, "id": "c52aef16", "metadata": {}, "outputs": [], "source": ["def build_ycsb_commands(num_replicas: int, workload_char: str) -> None:\n", "    # Ensure lowercase for consistency\n", "    wl_char = workload_char.lower()\n", "    \n", "    # Generate host string\n", "    hosts = \",\".join([f\"mongo-{i}.mongo\" for i in range(num_replicas)])\n", "    \n", "    # Common parts\n", "    workload_path = f\"workloads/workload{wl_char}\"\n", "    mongo_uri = f\"mongodb://{hosts}:27017/ycsb?replicaSet=rs0&readPreference=Primary\"\n", "    output_suffix = f\"wl{wl_char}_{num_replicas}_replicas_P.csv\"\n", "    \n", "    # Build load command\n", "    load_cmd = (\n", "        f\"./bin/ycsb load mongodb -s\\\\\\n\"\n", "        f\"  -P {workload_path} \\\\\\n\"\n", "        f\"  -p mongodb.url=\\\"{mongo_uri}\\\" \\\\\\n\"\n", "        f\"  -p recordcount=10000 \\\\\\n\"\n", "        f\"  > ycsb_results/ycsb_load_{output_suffix}\"\n", "    )\n", "\n", "    # Build run command\n", "    run_cmd = (\n", "        f\"./bin/ycsb run mongodb -s \\\\\\n\"\n", "        f\"  -P {workload_path} \\\\\\n\"\n", "        f\"  -p mongodb.url=\\\"{mongo_uri}\\\" \\\\\\n\"\n", "        f\"  -p operationcount=100000 \\\\\\n\"\n", "        f\"  > ycsb_results/ycsb_run_{output_suffix}\"\n", "    )\n", "\n", "    # Print both commands\n", "    print(\"Load command:\\n\" + load_cmd + \"\\n\")\n", "    print(\"Run command:\\n\" + run_cmd + \"\\n\")"]}, {"cell_type": "code", "execution_count": 27, "id": "87c82916", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Scaling up to: 7 replicas...\n", "\n", "Scaled mongo to 7 replicas.\n", "Adding replica with _id: 6\n", "Added new replica with _id: 6\n", "\n"]}], "source": ["scale_up(current_k8s_replicas + 1)"]}, {"cell_type": "code", "execution_count": 39, "id": "9df777a5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Scaling down to: 3 replicas...\n", "\n", "Scaled mongo to 3 replicas.\n", "Attempting to remove member with _id: 3 and host: mongo-3.mongo.default.svc.cluster.local:27017\n", "Removed member with _id: 3\n", "\n"]}], "source": ["scale_down(current_k8s_replicas - 1)"]}, {"cell_type": "code", "execution_count": 40, "id": "6e752fbe", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Currently running 3 replicas in Kubernetes.\n", "Currently running 3 replicas in MongoDB.\n", "\n"]}], "source": ["print(f\"Currently running {current_k8s_replicas} replicas in Kubernetes.\")\n", "print(f\"Currently running {current_mongodb_replicas} replicas in MongoDB.\\n\")"]}, {"cell_type": "code", "execution_count": 41, "id": "ef333f6d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Load command:\n", "./bin/ycsb load mongodb -s\\\n", "  -P workloads/workloadc \\\n", "  -p mongodb.url=\"mongodb://mongo-0.mongo,mongo-1.mongo,mongo-2.mongo:27017/ycsb?replicaSet=rs0&readPreference=SecondaryPreferred\" \\\n", "  -p recordcount=10000 \\\n", "  > ycsb_results/ycsb_load_wlc_3_replicas_S.csv\n", "\n", "Run command:\n", "./bin/ycsb run mongodb -s \\\n", "  -P workloads/workloadc \\\n", "  -p mongodb.url=\"mongodb://mongo-0.mongo,mongo-1.mongo,mongo-2.mongo:27017/ycsb?replicaSet=rs0&readPreference=SecondaryPreferred\" \\\n", "  -p operationcount=100000 \\\n", "  > ycsb_results/ycsb_run_wlc_3_replicas_S.csv\n", "\n"]}], "source": ["build_ycsb_commands(current_k8s_replicas, \"c\")\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 5}